<component name="libraryTable">
  <library name="Maven: org.slf4j:log4j-over-slf4j:1.7.26">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/slf4j/log4j-over-slf4j/1.7.26/log4j-over-slf4j-1.7.26.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/slf4j/log4j-over-slf4j/1.7.26/log4j-over-slf4j-1.7.26-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/slf4j/log4j-over-slf4j/1.7.26/log4j-over-slf4j-1.7.26-sources.jar!/" />
    </SOURCES>
  </library>
</component>