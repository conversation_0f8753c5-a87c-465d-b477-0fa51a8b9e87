<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="attpush" />
        <module name="WebSocket" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="attpush" target="1.8" />
      <module name="WebSocket" target="1.5" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_STRING" value="-encoding UTF8" />
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="WebSocket" options="" />
      <module name="attpush" options="" />
    </option>
  </component>
</project>